[2025-07-27 20:07:30] 🚀 开始生产版本barcode提取和更新任务
[2025-07-27 20:07:30] 📋 配置参数:
[2025-07-27 20:07:30]    - 批处理大小: 5,000
[2025-07-27 20:07:30]    - 最大记录数: 5,000
[2025-07-27 20:07:30]    - 输出文件: barcode_extraction_production.txt
[2025-07-27 20:07:30] ✅ 已连接到MongoDB数据库
[2025-07-27 20:07:30] ✅ 已确保id字段索引存在
[2025-07-27 20:09:07] ✅ 已确保barcode字段索引存在
[2025-07-27 20:09:07] 🔍 使用聚合管道查找有效barcode记录...
[2025-07-27 20:09:07] 🔄 开始处理，批处理大小: 5,000
[2025-07-27 20:09:07] 📝 限制处理记录数: 5,000
[2025-07-27 20:10:36] ⚠️ 接收到中断信号 2，正在优雅关闭...
[2025-07-27 20:11:13] 📊 开始处理记录...
[2025-07-27 20:11:13] ⚠️ 检测到停止请求，正在保存当前批次...
[2025-07-27 20:11:13] 
======================================================================
[2025-07-27 20:11:13] 📈 生产版本Barcode提取任务完成统计
[2025-07-27 20:11:13] ======================================================================
[2025-07-27 20:11:13] ✅ 总处理记录数: 0
[2025-07-27 20:11:13] 📝 成功更新记录数: 0
[2025-07-27 20:11:13] ❌ 错误记录数: 0
[2025-07-27 20:11:13] 0%
[2025-07-27 20:11:13] ⏱️ 总耗时: 223.49 秒 (3.7 分钟)
[2025-07-27 20:11:13] ⚡ 平均处理速度: 0.0 记录/秒
[2025-07-27 20:11:13] ======================================================================
[2025-07-27 20:11:13] 🔒 已关闭MongoDB连接
[2025-07-27 20:11:13] 
📄 详细日志已保存到: barcode_extraction_production.txt
