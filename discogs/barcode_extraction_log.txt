[2025-07-27 20:13:49] 🚀 开始barcode提取到CSV任务
[2025-07-27 20:13:49] 📋 配置参数:
[2025-07-27 20:13:49]    - 批处理大小: 10,000
[2025-07-27 20:13:49]    - 最大记录数: 50,000
[2025-07-27 20:13:49]    - CSV输出文件: barcode_data_50k.csv
[2025-07-27 20:13:49]    - 日志文件: barcode_extraction_log.txt
[2025-07-27 20:13:49] ✅ 已连接到MongoDB数据库
[2025-07-27 20:13:49] 🔍 使用聚合管道查找有效barcode记录...
[2025-07-27 20:13:49] 🔄 开始处理数据...
[2025-07-27 20:13:49] 📝 限制处理记录数: 50,000
[2025-07-27 20:14:56] 📊 开始收集barcode数据...
[2025-07-27 20:14:59] 🔄 进度: 已收集 10,000 条记录 (速度: 141.5 记录/秒)
[2025-07-27 20:14:59] 📝 写入批次数据到CSV: 10000 条记录
[2025-07-27 20:14:59] ✅ 成功写入 10000 条记录到CSV
[2025-07-27 20:15:01] 🔄 进度: 已收集 20,000 条记录 (速度: 276.0 记录/秒)
[2025-07-27 20:15:01] 📝 写入批次数据到CSV: 10000 条记录
[2025-07-27 20:15:01] ✅ 成功写入 10000 条记录到CSV
[2025-07-27 20:15:04] 🔄 进度: 已收集 30,000 条记录 (速度: 396.4 记录/秒)
[2025-07-27 20:15:04] 📝 写入批次数据到CSV: 10000 条记录
[2025-07-27 20:15:04] ✅ 成功写入 10000 条记录到CSV
[2025-07-27 20:15:08] 🔄 进度: 已收集 40,000 条记录 (速度: 502.8 记录/秒)
[2025-07-27 20:15:08] 📝 写入批次数据到CSV: 10000 条记录
[2025-07-27 20:15:08] ✅ 成功写入 10000 条记录到CSV
[2025-07-27 20:15:10] 🔄 进度: 已收集 50,000 条记录 (速度: 615.2 记录/秒)
[2025-07-27 20:15:10] 📝 写入批次数据到CSV: 10000 条记录
[2025-07-27 20:15:10] ✅ 成功写入 10000 条记录到CSV
[2025-07-27 20:15:10] 
======================================================================
[2025-07-27 20:15:10] 📈 Barcode提取到CSV任务完成统计
[2025-07-27 20:15:10] ======================================================================
[2025-07-27 20:15:10] ✅ 总处理记录数: 50,000
[2025-07-27 20:15:10] 📄 CSV输出文件: barcode_data_50k.csv
[2025-07-27 20:15:10] ⏱️ 总耗时: 81.33 秒 (1.4 分钟)
[2025-07-27 20:15:10] ⚡ 平均处理速度: 614.8 记录/秒
[2025-07-27 20:15:10] ======================================================================
[2025-07-27 20:15:10] 🔍 CSV文件验证: 包含 50,000 条数据记录
[2025-07-27 20:15:10] ✅ CSV文件样本数据:
[2025-07-27 20:15:10]    1. ID: 27789522, barcode: '.101421983'
[2025-07-27 20:15:10]    2. ID: 27482904, barcode: '.112791970'
[2025-07-27 20:15:10]    3. ID: 26808293, barcode: '.147351972'
[2025-07-27 20:15:10] 🔒 已关闭MongoDB连接
[2025-07-27 20:15:10] 
📄 详细日志已保存到: barcode_extraction_log.txt
