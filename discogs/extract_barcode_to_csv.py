#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从release表提取barcode数据并保存到CSV文件
从searchRuleRelease.baseinfo.barcode字段提取barcode值和对应的id，
并保存到CSV文件中

使用方法:
1. 测试运行: MAX_RECORDS=1000 python extract_barcode_to_csv.py
2. 生产运行: python extract_barcode_to_csv.py
3. 自定义输出: CSV_OUTPUT_FILE=my_barcode.csv python extract_barcode_to_csv.py

环境变量:
- MONGO_URI: MongoDB连接字符串
- DB_NAME: 数据库名称
- BATCH_SIZE: 批处理大小 (默认: 10000)
- MAX_RECORDS: 最大处理记录数，0表示处理全部 (默认: 0)
- CSV_OUTPUT_FILE: CSV输出文件路径 (默认: barcode_data.csv)
- OUTPUT_FILE: 日志文件路径 (默认: barcode_extraction_log.txt)
"""

import os
import csv
import time
from pymongo import MongoClient
from datetime import datetime

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '10000'))
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))
CSV_OUTPUT_FILE = os.getenv('CSV_OUTPUT_FILE', 'barcode_data.csv')
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'barcode_extraction_log.txt')

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    formatted_message = f"[{timestamp}] {message}"
    
    try:
        with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
            f.write(formatted_message + '\n')
    except Exception as e:
        print(f"写入日志文件失败: {e}")
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def write_barcode_to_csv(barcode_data, csv_filename):
    """将barcode数据写入CSV文件"""
    if not barcode_data:
        write_output("没有数据需要写入CSV文件")
        return
    
    # 定义CSV字段
    fieldnames = ['id', 'barcode']
    
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for data in barcode_data:
                writer.writerow({
                    'id': data.get('id', ''),
                    'barcode': data.get('barcode', '')
                })
        
        write_output(f"✅ 成功将 {len(barcode_data)} 条记录写入CSV文件: {csv_filename}")
        
    except Exception as e:
        write_output(f"❌ 写入CSV文件时出错: {e}")
        raise

def process_barcode_extraction():
    """主要的barcode提取处理函数"""
    start_time = time.time()
    write_output("🚀 开始barcode提取到CSV任务")
    write_output(f"📋 配置参数:")
    write_output(f"   - 批处理大小: {BATCH_SIZE:,}")
    write_output(f"   - 最大记录数: {'全部' if MAX_RECORDS == 0 else f'{MAX_RECORDS:,}'}")
    write_output(f"   - CSV输出文件: {CSV_OUTPUT_FILE}")
    write_output(f"   - 日志文件: {OUTPUT_FILE}")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    write_output("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取release集合
        release_collection = db['release']
        
        # 检查集合是否存在
        if 'release' not in db.list_collection_names():
            write_output("❌ 错误: release集合不存在")
            return False
        
        write_output("🔍 使用聚合管道查找有效barcode记录...")
        
        # 构建聚合管道
        pipeline = [
            # 匹配包含searchRuleRelease字段的记录
            {"$match": {"searchRuleRelease": {"$exists": True}}},
            
            # 投影并提取barcode
            {"$project": {
                "id": 1,
                "barcode": {
                    "$cond": {
                        "if": {"$and": [
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", None]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", ""]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", " "]}
                        ]},
                        "then": "$searchRuleRelease.baseinfo.barcode",
                        "else": None
                    }
                }
            }},
            
            # 只保留有效barcode的记录
            {"$match": {"barcode": {"$ne": None}}},
            
            # 限制记录数（如果设置了MAX_RECORDS）
            *([{"$limit": MAX_RECORDS}] if MAX_RECORDS > 0 else [])
        ]
        
        write_output(f"🔄 开始处理数据...")
        if MAX_RECORDS > 0:
            write_output(f"📝 限制处理记录数: {MAX_RECORDS:,}")
        
        # 处理统计
        processed_count = 0
        barcode_data = []
        
        # 执行聚合查询
        cursor = release_collection.aggregate(pipeline, allowDiskUse=True, batchSize=1000)
        
        write_output("📊 开始收集barcode数据...")
        
        for record in cursor:
            try:
                record_id = record.get('id')
                barcode = record.get('barcode')
                
                if record_id and barcode:
                    barcode_data.append({
                        'id': record_id,
                        'barcode': barcode
                    })
                    processed_count += 1
                    
                    # 显示进度
                    if processed_count % 10000 == 0:
                        elapsed_time = time.time() - start_time
                        speed = processed_count / elapsed_time if elapsed_time > 0 else 0
                        write_output(f"🔄 进度: 已收集 {processed_count:,} 条记录 "
                                   f"(速度: {speed:.1f} 记录/秒)")
                    
                    # 批量写入CSV（避免内存溢出）
                    if len(barcode_data) >= BATCH_SIZE:
                        write_output(f"📝 写入批次数据到CSV: {len(barcode_data)} 条记录")
                        
                        # 如果是第一批，创建新文件；否则追加
                        mode = 'w' if processed_count == len(barcode_data) else 'a'
                        write_header = (processed_count == len(barcode_data))
                        
                        try:
                            with open(CSV_OUTPUT_FILE, mode, newline='', encoding='utf-8') as csvfile:
                                writer = csv.DictWriter(csvfile, fieldnames=['id', 'barcode'])
                                if write_header:
                                    writer.writeheader()
                                
                                for data in barcode_data:
                                    writer.writerow(data)
                            
                            write_output(f"✅ 成功写入 {len(barcode_data)} 条记录到CSV")
                            
                        except Exception as e:
                            write_output(f"❌ 写入CSV失败: {e}")
                            return False
                        
                        # 清空内存中的数据
                        barcode_data = []
                
            except Exception as e:
                write_output(f"❌ 处理记录时出错: {e}", False)
                continue
        
        # 处理剩余数据
        if barcode_data:
            write_output(f"📝 写入最后批次数据到CSV: {len(barcode_data)} 条记录")
            
            mode = 'w' if processed_count == len(barcode_data) else 'a'
            write_header = (processed_count == len(barcode_data))
            
            try:
                with open(CSV_OUTPUT_FILE, mode, newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=['id', 'barcode'])
                    if write_header:
                        writer.writeheader()
                    
                    for data in barcode_data:
                        writer.writerow(data)
                
                write_output(f"✅ 成功写入最后 {len(barcode_data)} 条记录到CSV")
                
            except Exception as e:
                write_output(f"❌ 写入最后批次CSV失败: {e}")
                return False
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 输出最终统计
        write_output("\n" + "="*70)
        write_output("📈 Barcode提取到CSV任务完成统计")
        write_output("="*70)
        write_output(f"✅ 总处理记录数: {processed_count:,}")
        write_output(f"📄 CSV输出文件: {CSV_OUTPUT_FILE}")
        write_output(f"⏱️ 总耗时: {processing_time:.2f} 秒 ({processing_time/60:.1f} 分钟)")
        write_output(f"⚡ 平均处理速度: {processed_count/processing_time:.1f} 记录/秒" if processing_time > 0 else "N/A")
        write_output("="*70)
        
        # 验证CSV文件
        if os.path.exists(CSV_OUTPUT_FILE):
            try:
                with open(CSV_OUTPUT_FILE, 'r', encoding='utf-8') as f:
                    reader = csv.reader(f)
                    csv_lines = sum(1 for _ in reader)
                    csv_records = csv_lines - 1  # 减去表头
                
                write_output(f"🔍 CSV文件验证: 包含 {csv_records:,} 条数据记录")
                
                # 显示前几条样本数据
                with open(CSV_OUTPUT_FILE, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    samples = []
                    for i, row in enumerate(reader):
                        if i >= 3:  # 只显示前3条
                            break
                        samples.append(row)
                
                if samples:
                    write_output("✅ CSV文件样本数据:")
                    for i, sample in enumerate(samples, 1):
                        write_output(f"   {i}. ID: {sample['id']}, barcode: '{sample['barcode']}'")
                
            except Exception as e:
                write_output(f"⚠️ CSV文件验证失败: {e}")
        
        return True
        
    except Exception as e:
        write_output(f"❌ 处理过程中出错: {e}")
        return False
    finally:
        # 关闭数据库连接
        client.close()
        write_output("🔒 已关闭MongoDB连接")
        write_output(f"\n📄 详细日志已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    try:
        success = process_barcode_extraction()
        
        if success:
            print(f"\n✅ Barcode提取到CSV任务完成！")
            print(f"📄 CSV文件: {CSV_OUTPUT_FILE}")
            print(f"📄 详细日志: {OUTPUT_FILE}")
        else:
            print(f"\n❌ 任务执行失败！详细信息请查看: {OUTPUT_FILE}")
            
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        print(f"📄 详细日志请查看: {OUTPUT_FILE}")
